import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Switch,
  Alert,
  RefreshControl,
} from 'react-native';
import { router } from 'expo-router';
import {
  User,
  Settings,
  Bell,
  Shield,
  Palette,
  Database,
  HelpCircle,
  LogOut,
  ChevronRight,
  Sparkles,
  Heart,
  Star,
  Coffee,
  MessageCircle,
} from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withSequence,
  withTiming,
  interpolateColor,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/colors';
import { typography, spacing, radius } from '@/constants/theme';
import { useAuthStore } from '@/stores/auth-store';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { useAnimationsEnabled, useHapticsEnabled, useWhimsyStore } from '@/stores/whimsy-store';

// Animated components
const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
const AnimatedView = Animated.createAnimatedComponent(View);

// Profile Header Component
const ProfileHeader: React.FC = () => {
  const { user, salon } = useAuthStore();
  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);
  const colorProgress = useSharedValue(0);
  const animationsEnabled = useAnimationsEnabled();

  // Breathing animation
  useEffect(() => {
    if (animationsEnabled) {
      scale.value = withRepeat(
        withSequence(withTiming(1.02, { duration: 2000 }), withTiming(1, { duration: 2000 })),
        -1,
        true
      );
      colorProgress.value = withRepeat(withTiming(1, { duration: 3000 }), -1, true);
    }
  }, [animationsEnabled, scale, colorProgress]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }, { rotate: `${rotation.value}deg` }],
  }));

  const animatedBorderStyle = useAnimatedStyle(() => ({
    borderColor: interpolateColor(
      colorProgress.value,
      [0, 0.5, 1],
      [Colors.light.primary, Colors.light.secondary, Colors.light.primary]
    ),
  }));

  const handleAvatarPress = () => {
    rotation.value = withSequence(
      withSpring(5, { damping: 15 }),
      withSpring(-5, { damping: 15 }),
      withSpring(0, { damping: 15 })
    );
  };

  return (
    <LinearGradient
      colors={[Colors.light.primary + '20', Colors.light.secondary + '20']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.profileHeader}
    >
      <TouchableOpacity onPress={handleAvatarPress} activeOpacity={0.8}>
        <Animated.View style={[styles.avatarContainer, animatedBorderStyle]}>
          <AnimatedView style={[styles.avatar, animatedStyle]}>
            <User size={40} color={Colors.light.primary} />
          </AnimatedView>
        </Animated.View>
      </TouchableOpacity>

      <View style={styles.profileInfo}>
        <Text style={styles.userName}>{user?.email?.split('@')[0] || 'Usuario'}</Text>
        <Text style={styles.salonName}>{salon?.name || 'Mi Salón'}</Text>
        <View style={styles.statusBadge}>
          <Sparkles size={12} color={Colors.light.primary} />
          <Text style={styles.statusText}>Colorista Pro</Text>
        </View>
      </View>
    </LinearGradient>
  );
};

// Settings Section Component
const SettingsSection: React.FC<{
  title: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  children: React.ReactNode;
}> = ({ title, icon: Icon, children }) => {
  return (
    <View style={styles.settingsSection}>
      <View style={styles.sectionHeader}>
        <Icon size={20} color={Colors.light.primary} />
        <Text style={styles.sectionTitle}>{title}</Text>
      </View>
      {children}
    </View>
  );
};

// Settings Item Component
const SettingsItem: React.FC<{
  title: string;
  subtitle?: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  onPress?: () => void;
  rightElement?: React.ReactNode;
  showChevron?: boolean;
}> = ({ title, subtitle, icon: Icon, onPress, rightElement, showChevron = true }) => {
  const scale = useSharedValue(1);
  const hapticsEnabled = useHapticsEnabled();

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.98);
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  const handlePress = () => {
    if (hapticsEnabled) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    onPress?.();
  };

  return (
    <AnimatedTouchableOpacity
      style={[styles.settingsItem, animatedStyle]}
      onPress={handlePress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={!onPress}
      activeOpacity={1}
    >
      <View style={styles.itemLeft}>
        <View style={styles.itemIcon}>
          <Icon size={20} color={Colors.light.primary} />
        </View>
        <View style={styles.itemContent}>
          <Text style={styles.itemTitle}>{title}</Text>
          {subtitle && <Text style={styles.itemSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      <View style={styles.itemRight}>
        {rightElement}
        {showChevron && onPress && (
          <ChevronRight size={16} color={Colors.light.textSecondary} style={styles.chevron} />
        )}
      </View>
    </AnimatedTouchableOpacity>
  );
};

export default function ProfileScreen() {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { logout } = useAuthStore();
  const {
    enableAnimations,
    enableHapticFeedback,
    enableEasterEggs,
    totalInteractions,
    toggleAnimations,
    toggleHaptics,
    toggleEasterEggs,
  } = useWhimsyStore();

  const handleRefresh = async () => {
    setIsRefreshing(true);
    const hapticsEnabled = useWhimsyStore.getState().enableHapticFeedback;

    try {
      // Simulate data refresh
      await new Promise(resolve => setTimeout(resolve, 1000));
      if (hapticsEnabled) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleLogout = () => {
    Alert.alert('Cerrar Sesión', '¿Estás seguro de que quieres cerrar sesión?', [
      { text: 'Cancelar', style: 'cancel' },
      {
        text: 'Cerrar Sesión',
        style: 'destructive',
        onPress: () => logout(),
      },
    ]);
  };

  const navigateToSettings = (screen: string) => {
    router.push(`/settings/${screen}`);
  };

  const navigateToLegacyScreen = (screen: string) => {
    router.push(`/${screen}`);
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={handleRefresh}
          tintColor={Colors.light.primary}
          colors={[Colors.light.primary]}
        />
      }
      showsVerticalScrollIndicator={false}
    >
      {/* Profile Header */}
      <ProfileHeader />

      {/* Quick Stats */}
      <View style={styles.quickStats}>
        <View style={styles.statItem}>
          <Heart size={16} color={Colors.light.primary} />
          <Text style={styles.statNumber}>{totalInteractions}</Text>
          <Text style={styles.statLabel}>Interacciones</Text>
        </View>
        <View style={styles.statItem}>
          <Star size={16} color={Colors.light.secondary} />
          <Text style={styles.statNumber}>Pro</Text>
          <Text style={styles.statLabel}>Nivel</Text>
        </View>
        <View style={styles.statItem}>
          <Coffee size={16} color={Colors.light.success} />
          <Text style={styles.statNumber}>∞</Text>
          <Text style={styles.statLabel}>Energía</Text>
        </View>
      </View>

      {/* Experience Settings */}
      <SettingsSection title="Experiencia" icon={Sparkles}>
        <SettingsItem
          title="Animaciones"
          subtitle="Efectos visuales y transiciones"
          icon={Sparkles}
          rightElement={
            <Switch
              value={enableAnimations}
              onValueChange={toggleAnimations}
              trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
              thumbColor={Colors.light.surface}
            />
          }
          showChevron={false}
        />
        <SettingsItem
          title="Feedback Táctil"
          subtitle="Vibraciones en interacciones"
          icon={Bell}
          rightElement={
            <Switch
              value={enableHapticFeedback}
              onValueChange={toggleHaptics}
              trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
              thumbColor={Colors.light.surface}
            />
          }
          showChevron={false}
        />
        <SettingsItem
          title="Easter Eggs"
          subtitle="Sorpresas ocultas en la app"
          icon={Heart}
          rightElement={
            <Switch
              value={enableEasterEggs}
              onValueChange={toggleEasterEggs}
              trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
              thumbColor={Colors.light.surface}
            />
          }
          showChevron={false}
        />
      </SettingsSection>

      {/* Chat & IA */}
      <SettingsSection title="Chat & IA" icon={MessageCircle}>
        <SettingsItem
          title="Conversaciones"
          subtitle="Historial y configuración de chat"
          icon={MessageCircle}
          onPress={() => router.push('/')}
        />
        <SettingsItem
          title="Asistente IA"
          subtitle="Configurar personalidad del asistente"
          icon={Sparkles}
          onPress={() => navigateToLegacyScreen('assistant')}
        />
      </SettingsSection>

      {/* Business Settings */}
      <SettingsSection title="Mi Negocio" icon={Settings}>
        <SettingsItem
          title="Información del Salón"
          subtitle="Nombre, dirección y configuración básica"
          icon={User}
          onPress={() => navigateToSettings('business')}
        />
        <SettingsItem
          title="Inventario"
          subtitle="Marcas, productos y configuración"
          icon={Palette}
          onPress={() => navigateToLegacyScreen('inventory')}
        />
        <SettingsItem
          title="Clientes"
          subtitle="Gestión de clientes y historiales"
          icon={User}
          onPress={() => navigateToLegacyScreen('clients')}
        />
        <SettingsItem
          title="Precios y Facturación"
          subtitle="Configurar precios de servicios"
          icon={Settings}
          onPress={() => navigateToSettings('pricing')}
        />
      </SettingsSection>

      {/* Privacy & Security */}
      <SettingsSection title="Privacidad y Seguridad" icon={Shield}>
        <SettingsItem
          title="Privacidad"
          subtitle="Gestión de datos y permisos"
          icon={Shield}
          onPress={() => navigateToSettings('security')}
        />
        <SettingsItem
          title="Copia de Seguridad"
          subtitle="Respaldo automático de datos"
          icon={Database}
          onPress={() => navigateToSettings('backup')}
        />
      </SettingsSection>

      {/* Support */}
      <SettingsSection title="Soporte" icon={HelpCircle}>
        <SettingsItem
          title="Centro de Ayuda"
          subtitle="Guías y tutoriales"
          icon={HelpCircle}
          onPress={() => navigateToSettings('help')}
        />
        <SettingsItem
          title="Contactar Soporte"
          subtitle="Obtener ayuda del equipo"
          icon={MessageCircle}
          onPress={() => navigateToSettings('support')}
        />
      </SettingsSection>

      {/* Logout */}
      <View style={styles.logoutSection}>
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout} activeOpacity={0.8}>
          <LogOut size={20} color={Colors.light.error} />
          <Text style={styles.logoutText}>Cerrar Sesión</Text>
        </TouchableOpacity>
      </View>

      {/* App Info */}
      <View style={styles.appInfo}>
        <Text style={styles.appVersion}>Salonier v2.2.0</Text>
        <Text style={styles.appBuild}>Build 2024.12.27</Text>
        <Text style={styles.appCredits}>Con 💜 por Oscar Cortijo & Claude AI</Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  contentContainer: {
    paddingBottom: spacing.xl,
  },

  // Profile Header
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing.lg,
    marginHorizontal: spacing.lg,
    marginTop: spacing.md,
    marginBottom: spacing.lg,
    borderRadius: radius.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  avatarContainer: {
    borderWidth: 3,
    borderRadius: 40,
    padding: 3,
  },
  avatar: {
    width: 72,
    height: 72,
    borderRadius: 36,
    backgroundColor: Colors.light.surface,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileInfo: {
    flex: 1,
    marginLeft: spacing.md,
  },
  userName: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  salonName: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
    marginBottom: spacing.xs,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.primary + '15',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.full,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.primary,
    marginLeft: spacing.xs,
  },

  // Quick Stats
  quickStats: {
    flexDirection: 'row',
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    backgroundColor: Colors.light.card,
    borderRadius: radius.lg,
    padding: spacing.md,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginTop: spacing.xs,
    marginBottom: 2,
  },
  statLabel: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },

  // Settings Sections
  settingsSection: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    backgroundColor: Colors.light.card,
    borderRadius: radius.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  sectionTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginLeft: spacing.sm,
  },

  // Settings Items
  settingsItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border + '30',
  },
  itemLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.primary + '10',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.sm,
  },
  itemContent: {
    flex: 1,
  },
  itemTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    marginBottom: 2,
  },
  itemSubtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
  itemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  chevron: {
    marginLeft: spacing.sm,
  },

  // Logout Section
  logoutSection: {
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.card,
    paddingVertical: spacing.md,
    borderRadius: radius.lg,
    borderWidth: 1,
    borderColor: Colors.light.error + '20',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  logoutText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.error,
    marginLeft: spacing.sm,
  },

  // App Info
  appInfo: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },
  appVersion: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.textSecondary,
    marginBottom: 2,
  },
  appBuild: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary + '80',
    marginBottom: spacing.sm,
  },
  appCredits: {
    fontSize: typography.sizes.xs,
    color: Colors.light.primary,
    fontStyle: 'italic',
  },
});
