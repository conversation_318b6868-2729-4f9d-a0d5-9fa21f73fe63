import React from 'react';
import { Tabs } from 'expo-router';
import { MessageCircle, Briefcase, User } from 'lucide-react-native';
import { Platform } from 'react-native';

import Colors from '@/constants/colors';
import { shadows, typography, layout } from '@/constants/theme';

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors.light.primary,
        tabBarInactiveTintColor: Colors.light.tabIconDefault,
        headerShown: true,
        tabBarStyle: {
          backgroundColor: Colors.light.surface,
          borderTopWidth: 0,
          height: layout.tabBarHeight,
          paddingBottom: Platform.OS === 'ios' ? 20 : 15,
          paddingTop: 10,
          ...shadows.md,
          shadowOffset: { width: 0, height: -2 },
        },
        tabBarItemStyle: {
          paddingVertical: 5,
        },
        tabBarLabelStyle: {
          fontSize: typography.sizes.xs,
          fontWeight: typography.weights.medium,
        },
        headerStyle: {
          backgroundColor: Colors.light.background,
          elevation: 0,
          shadowOpacity: 0,
          borderBottomWidth: 1,
          borderBottomColor: Colors.light.border,
        },
        headerTitleStyle: {
          fontWeight: typography.weights.bold,
          fontSize: typography.sizes['2xl'],
          color: Colors.light.text,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Chat',
          tabBarIcon: ({ color, size }) => <MessageCircle size={size} color={color} />,
          headerTitle: 'Salonier Assistant',
          headerShown: false, // Hide header for clean chat experience
          tabBarAccessibilityLabel: 'Chat con Salonier Assistant',
        }}
      />
      <Tabs.Screen
        name="workspace"
        options={{
          title: 'Workspace',
          tabBarIcon: ({ color, size }) => <Briefcase size={size} color={color} />,
          headerTitle: 'Mi Workspace',
          tabBarAccessibilityLabel: 'Acceder a herramientas de trabajo',
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Perfil',
          tabBarIcon: ({ color, size }) => <User size={size} color={color} />,
          headerTitle: 'Mi Perfil',
          tabBarAccessibilityLabel: 'Ver perfil y configuración',
        }}
      />
      {/* Hide old screens - keep for backward compatibility */}
      <Tabs.Screen
        name="clients"
        options={{
          href: null, // Hide from tab bar
        }}
      />
      <Tabs.Screen
        name="inventory"
        options={{
          href: null, // Hide from tab bar
        }}
      />
      <Tabs.Screen
        name="assistant"
        options={{
          href: null, // Hide from tab bar
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          href: null, // Hide from tab bar
        }}
      />
    </Tabs>
  );
}
