/**
 * Nueva pantalla principal centrada en Chat Hub
 * Reemplaza el dashboard tradicional con una experiencia conversacional
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { X } from 'lucide-react-native';

import Colors from '@/constants/colors';
import { commonStyles } from '@/styles/commonStyles';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { useServiceDraftStore } from '@/stores/service-draft-store';
import InventoryReports from '@/components/reports/InventoryReports';
import ChatHub from '@/components/chat/ChatHub';

export default function HomeScreen() {
  const [showReportsModal, setShowReportsModal] = useState(false);
  const { configuration } = useSalonConfigStore();
  const { getPendingDrafts, deleteDraft } = useServiceDraftStore();

  const handleProductSelect = (productId: string) => {
    setShowReportsModal(false);
    router.push(`/inventory/${productId}`);
  };

  // Check for pending service drafts on mount
  useEffect(() => {
    const checkPendingDrafts = async () => {
      const drafts = getPendingDrafts();

      if (drafts.length > 0) {
        // Show the most recent draft
        const mostRecentDraft = drafts.sort(
          (a, b) => new Date(b.lastSaved).getTime() - new Date(a.lastSaved).getTime()
        )[0];

        const timeAgo = getTimeAgo(mostRecentDraft.lastSaved);

        Alert.alert(
          'Servicio pendiente',
          `Tienes un servicio sin terminar para ${mostRecentDraft.clientName} (guardado ${timeAgo})`,
          [
            {
              text: 'Descartar',
              style: 'destructive',
              onPress: () => {
                deleteDraft(mostRecentDraft.id);
              },
            },
            {
              text: 'Continuar',
              onPress: () => {
                router.push({
                  pathname: '/service/new',
                  params: {
                    clientId: mostRecentDraft.clientId,
                    draftId: mostRecentDraft.id,
                  },
                });
              },
            },
          ]
        );
      }
    };

    // Delay check slightly to avoid immediate alerts
    const timer = setTimeout(checkPendingDrafts, 500);
    return () => clearTimeout(timer);
  }, [deleteDraft, getPendingDrafts]);

  const getTimeAgo = (dateString: string): string => {
    const now = new Date();
    const date = new Date(dateString);
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) {
      return 'hace un momento';
    } else if (diffMins < 60) {
      return `hace ${diffMins} minuto${diffMins === 1 ? '' : 's'}`;
    } else if (diffMins < 1440) {
      const hours = Math.floor(diffMins / 60);
      return `hace ${hours} hora${hours > 1 ? 's' : ''}`;
    } else {
      const days = Math.floor(diffMins / 1440);
      return `hace ${days} día${days > 1 ? 's' : ''}`;
    }
  };

  return (
    <View style={styles.container}>
      {/* Chat Hub como pantalla principal */}
      <ChatHub
        onNavigateToService={() => router.push('/service/client-selection')}
        onNavigateToClients={() => router.push('/clients')}
        onNavigateToInventory={() => router.push('/inventory')}
        onNavigateToReports={() => setShowReportsModal(true)}
      />

      {/* Modal de reportes - mantener funcionalidad existente */}
      <Modal visible={showReportsModal} animationType="slide" presentationStyle="pageSheet">
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowReportsModal(false)}>
              <X size={24} color={Colors.light.gray} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Reportes de Inventario</Text>
            <View style={commonStyles.width24} />
          </View>

          <InventoryReports onProductSelect={handleProductSelect} />
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.light.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.light.text,
  },
});
