import React, { useState, useEffect, useRef, Suspense } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Modal,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { router } from 'expo-router';
import {
  Zap,
  Users,
  Package,
  BarChart3,
  Sparkles,
  TrendingUp,
  Shield,
  X,
  Plus,
  MessageSquare,
} from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withRepeat,
  withSequence,
  withTiming,
  interpolate,
  withDelay,
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/colors';
import { commonStyles } from '@/styles/commonStyles';
import { typography, spacing, radius } from '@/constants/theme';
import { useSalonConfigStore } from '@/stores/salon-config-store';
import { useServiceDraftStore } from '@/stores/service-draft-store';
import { useDashboardStore } from '@/stores/dashboard-store';
import { useAnimationsEnabled, useHapticsEnabled, useWhimsyStore } from '@/stores/whimsy-store';
import { LazyComponents } from '@/utils/lazy-components';

// Animated components
const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);
const AnimatedView = Animated.createAnimatedComponent(View);

// Fallback for reports modal
const ReportsLoadingFallback = () => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color={Colors.light.primary} />
  </View>
);

// Floating Action Button Component
const FloatingActionButton: React.FC = () => {
  const scale = useSharedValue(1);
  const rotation = useSharedValue(0);
  const [isExpanded, setIsExpanded] = useState(false);
  const hapticsEnabled = useHapticsEnabled();
  const animationsEnabled = useAnimationsEnabled();

  // Breathing animation
  useEffect(() => {
    if (animationsEnabled && !isExpanded) {
      scale.value = withRepeat(
        withSequence(withTiming(1.05, { duration: 1500 }), withTiming(1, { duration: 1500 })),
        -1,
        true
      );
    } else {
      scale.value = withTiming(1, { duration: 300 });
    }
  }, [animationsEnabled, isExpanded, scale]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }, { rotate: `${rotation.value}deg` }],
  }));

  const handlePress = () => {
    if (hapticsEnabled) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }

    setIsExpanded(!isExpanded);
    rotation.value = withSpring(isExpanded ? 0 : 45, { damping: 15 });
  };

  const quickActions = [
    {
      icon: Zap,
      label: 'Nuevo Servicio',
      action: () => router.push('/service/client-selection'),
      color: Colors.light.primary,
    },
    {
      icon: Users,
      label: 'Clientes',
      action: () => router.push('/clients'),
      color: Colors.light.secondary,
    },
    {
      icon: Package,
      label: 'Inventario',
      action: () => router.push('/inventory'),
      color: Colors.light.success,
    },
    {
      icon: MessageSquare,
      label: 'Chat',
      action: () => router.push('/'),
      color: Colors.light.primary,
    },
  ];

  return (
    <>
      {/* Backdrop */}
      {isExpanded && (
        <TouchableOpacity
          style={styles.fabBackdrop}
          activeOpacity={1}
          onPress={() => setIsExpanded(false)}
        />
      )}

      {/* Quick Actions */}
      {isExpanded && (
        <View style={styles.quickActionsContainer}>
          {quickActions.map((action, index) => (
            <AnimatedView
              key={action.label}
              entering={animationsEnabled ? undefined : undefined}
              style={[styles.quickActionItem, { bottom: 80 + index * 60 }]}
            >
              <TouchableOpacity
                style={[styles.quickActionButton, { backgroundColor: action.color }]}
                onPress={() => {
                  if (hapticsEnabled) {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }
                  setIsExpanded(false);
                  action.action();
                }}
                activeOpacity={0.8}
              >
                <action.icon size={20} color="white" />
              </TouchableOpacity>
              <Text style={styles.quickActionLabel}>{action.label}</Text>
            </AnimatedView>
          ))}
        </View>
      )}

      {/* Main FAB */}
      <AnimatedTouchableOpacity
        style={[styles.fab, animatedStyle]}
        onPress={handlePress}
        activeOpacity={0.9}
      >
        <Plus size={24} color="white" />
      </AnimatedTouchableOpacity>
    </>
  );
};

// Animated logo with easter egg
const AnimatedLogo: React.FC = () => {
  const rotation = useSharedValue(0);
  const scale = useSharedValue(1);
  const rainbow = useSharedValue(0);
  const [easterEggCount, setEasterEggCount] = useState(0);
  const [showEasterEgg, setShowEasterEgg] = useState(false);
  const lastTap = useRef(0);
  const hapticsEnabled = useHapticsEnabled();
  const { incrementMetric } = useWhimsyStore.getState();
  const easterEggsEnabled = useWhimsyStore(state => state.enableEasterEggs);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }, { scale: scale.value }],
  }));

  const rainbowStyle = useAnimatedStyle(() => ({
    opacity: interpolate(rainbow.value, [0, 1], [0, 0.3]),
  }));

  const handleLogoPress = () => {
    const now = Date.now();
    if (now - lastTap.current < 500) {
      // Double tap within 500ms
      setEasterEggCount(prev => prev + 1);

      // Easter egg activation after 5 taps (only if enabled)
      if (easterEggCount >= 4 && easterEggsEnabled) {
        setShowEasterEgg(true);
        if (hapticsEnabled) {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        }

        // Track easter egg discovery
        incrementMetric('easterEggsFound');

        // Rainbow + spin animation
        rainbow.value = withRepeat(withTiming(1, { duration: 200 }), 10, true);
        rotation.value = withSequence(
          withTiming(360, { duration: 1000 }),
          withTiming(720, { duration: 500 })
        );

        Alert.alert(
          '🎨 ¡Salonier Easter Egg!',
          '¡Has desbloqueado el Modo Colorista Experto!\n\nEquipo: Oscar Cortijo & Claude AI\nVersión: 2.2.0\n\n✨ ¡Sigue creando belleza!',
          [{ text: '¡Genial!', onPress: () => setEasterEggCount(0) }]
        );

        setTimeout(() => {
          setShowEasterEgg(false);
          rainbow.value = withTiming(0, { duration: 1000 });
        }, 3000);
      } else {
        // Small bounce animation
        scale.value = withSequence(withSpring(1.1, { damping: 10 }), withSpring(1, { damping: 8 }));
        if (hapticsEnabled) {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
      }
    }

    lastTap.current = now;
  };

  return (
    <TouchableOpacity onPress={handleLogoPress} activeOpacity={0.8}>
      <View style={commonStyles.positionRelative}>
        {/* Rainbow background for easter egg */}
        <Animated.View style={[styles.rainbowBg, rainbowStyle]} />

        <Animated.Text style={[styles.title, animatedStyle]}>
          Salonier {showEasterEgg && '✨'}
        </Animated.Text>

        {/* Progress indicator for easter egg */}
        {easterEggCount > 0 && easterEggCount < 5 && (
          <View style={styles.easterEggProgress}>
            {[...Array(5)].map((_, i) => (
              <View
                key={i}
                style={[
                  styles.easterEggDot,
                  {
                    backgroundColor:
                      i < easterEggCount ? Colors.light.primary : Colors.light.border,
                  },
                ]}
              />
            ))}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

// Breathing card component
const BreathingCard: React.FC<{
  children: React.ReactNode;
  onPress?: () => void;
  delay?: number;
}> = ({ children, onPress, delay = 0 }) => {
  const breatheScale = useSharedValue(1);
  const pressScale = useSharedValue(1);
  const animationsEnabled = useAnimationsEnabled();
  const hapticsEnabled = useHapticsEnabled();

  useEffect(() => {
    // Only animate if enabled
    if (animationsEnabled) {
      breatheScale.value = withDelay(
        delay,
        withRepeat(
          withSequence(withTiming(1.02, { duration: 2000 }), withTiming(1, { duration: 2000 })),
          -1,
          true
        )
      );
    }
  }, [delay, animationsEnabled, breatheScale]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: breatheScale.value * pressScale.value }],
  }));

  const handlePressIn = () => {
    pressScale.value = withSpring(0.98);
  };

  const handlePressOut = () => {
    pressScale.value = withSpring(1);
  };

  const handlePress = () => {
    if (hapticsEnabled) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    onPress?.();
  };

  if (onPress) {
    return (
      <AnimatedTouchableOpacity
        style={animatedStyle}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        {children}
      </AnimatedTouchableOpacity>
    );
  }

  return <AnimatedView style={animatedStyle}>{children}</AnimatedView>;
};

export default function WorkspaceScreen() {
  const [showReportsModal, setShowReportsModal] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { configuration } = useSalonConfigStore();
  const { getPendingDrafts, deleteDraft } = useServiceDraftStore();
  const { metrics, isLoading, loadTodayMetrics, startAutoRefresh } = useDashboardStore();

  const handleProductSelect = (productId: string) => {
    setShowReportsModal(false);
    router.push(`/inventory/${productId}`);
  };

  // Check for pending service drafts on mount
  useEffect(() => {
    const checkPendingDrafts = async () => {
      const drafts = getPendingDrafts();

      if (drafts.length > 0) {
        // Show the most recent draft
        const mostRecentDraft = drafts.sort(
          (a, b) => new Date(b.lastSaved).getTime() - new Date(a.lastSaved).getTime()
        )[0];

        const timeAgo = getTimeAgo(mostRecentDraft.lastSaved);

        Alert.alert(
          'Servicio pendiente',
          `Tienes un servicio sin terminar para ${mostRecentDraft.clientName} (guardado ${timeAgo})`,
          [
            {
              text: 'Descartar',
              style: 'destructive',
              onPress: () => {
                deleteDraft(mostRecentDraft.id);
              },
            },
            {
              text: 'Continuar',
              onPress: () => {
                router.push({
                  pathname: '/service/new',
                  params: {
                    clientId: mostRecentDraft.clientId,
                    restoreDraft: 'true',
                  },
                });
              },
            },
          ],
          { cancelable: true }
        );
      }
    };

    const timer = setTimeout(checkPendingDrafts, 500);
    return () => clearTimeout(timer);
  }, [deleteDraft, getPendingDrafts]);

  // Load dashboard metrics on mount and set up auto-refresh
  useEffect(() => {
    loadTodayMetrics();
    const cleanup = startAutoRefresh();
    return cleanup;
  }, [loadTodayMetrics, startAutoRefresh]);

  // Custom refresh handler
  const handleRefresh = async () => {
    setIsRefreshing(true);
    const hapticsEnabled = useWhimsyStore.getState().enableHapticFeedback;

    try {
      await loadTodayMetrics();
      if (hapticsEnabled) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    } finally {
      setTimeout(() => setIsRefreshing(false), 800);
    }
  };

  const getTimeAgo = (date: Date) => {
    const now = new Date();
    const saved = new Date(date);
    const diffMs = now.getTime() - saved.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffMs / 60000);

    if (diffSecs < 60) {
      return `hace unos segundos`;
    } else if (diffMins < 60) {
      return `hace ${diffMins} minuto${diffMins === 1 ? '' : 's'}`;
    } else if (diffMins < 1440) {
      const hours = Math.floor(diffMins / 60);
      return `hace ${hours} hora${hours > 1 ? 's' : ''}`;
    } else {
      const days = Math.floor(diffMins / 1440);
      return `hace ${days} día${days > 1 ? 's' : ''}`;
    }
  };

  return (
    <>
      <ScrollView
        style={styles.container}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor={Colors.light.primary}
            colors={[Colors.light.primary]}
            progressBackgroundColor="white"
          />
        }
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <AnimatedLogo />
          <Text style={styles.subtitle}>Tu Workspace de Coloración Profesional</Text>
        </View>

        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>Acciones Principales</Text>

          <BreathingCard onPress={() => router.push('/service/client-selection')}>
            <LinearGradient
              colors={[Colors.light.primary, Colors.light.secondary]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.primaryAction}
            >
              <View style={styles.actionIcon}>
                <Zap size={28} color="white" />
              </View>
              <View style={styles.actionContent}>
                <Text style={styles.actionTitle}>Nuevo Servicio</Text>
                <Text style={styles.actionSubtitle}>
                  Diagnóstico capilar inteligente con selección de cliente
                </Text>
              </View>
              <Sparkles size={24} color="white" />
            </LinearGradient>
          </BreathingCard>

          <View style={styles.secondaryActions}>
            <BreathingCard onPress={() => router.push('/')} delay={100}>
              <View style={styles.secondaryAction}>
                <MessageSquare size={24} color={Colors.light.primary} />
                <Text style={styles.secondaryActionText}>Chat IA</Text>
              </View>
            </BreathingCard>

            <BreathingCard onPress={() => router.push('/clients')} delay={150}>
              <View style={styles.secondaryAction}>
                <Users size={24} color={Colors.light.secondary} />
                <Text style={styles.secondaryActionText}>Clientes</Text>
              </View>
            </BreathingCard>

            <BreathingCard onPress={() => router.push('/inventory')} delay={200}>
              <View style={styles.secondaryAction}>
                <Package size={24} color={Colors.light.success} />
                <Text style={styles.secondaryActionText}>Inventario</Text>
              </View>
            </BreathingCard>

            {configuration.inventoryControlLevel !== 'solo-formulas' && (
              <BreathingCard onPress={() => setShowReportsModal(true)} delay={250}>
                <View style={styles.secondaryAction}>
                  <BarChart3 size={24} color={Colors.light.warning} />
                  <Text style={styles.secondaryActionText}>Reportes</Text>
                </View>
              </BreathingCard>
            )}
          </View>
        </View>

        <View style={styles.statsSection}>
          <Text style={styles.sectionTitle}>Resumen de Hoy</Text>

          <View style={styles.statsGrid}>
            <BreathingCard onPress={() => router.push('/clients')} delay={100}>
              <View style={styles.statCard}>
                <View style={styles.statIcon}>
                  <Zap size={20} color={Colors.light.primary} />
                </View>
                {isLoading ? (
                  <ActivityIndicator
                    size="small"
                    color={Colors.light.primary}
                    style={commonStyles.marginVertical8}
                  />
                ) : (
                  <Text style={styles.statNumber}>{metrics.servicestoday}</Text>
                )}
                <Text style={styles.statLabel}>Servicios Hoy</Text>
              </View>
            </BreathingCard>

            <BreathingCard onPress={() => router.push('/clients')} delay={200}>
              <View style={styles.statCard}>
                <View style={styles.statIcon}>
                  <Users size={20} color={Colors.light.primary} />
                </View>
                {isLoading ? (
                  <ActivityIndicator
                    size="small"
                    color={Colors.light.primary}
                    style={commonStyles.marginVertical8}
                  />
                ) : (
                  <Text style={styles.statNumber}>{metrics.totalClients}</Text>
                )}
                <Text style={styles.statLabel}>Clientes</Text>
              </View>
            </BreathingCard>

            <BreathingCard onPress={() => setShowReportsModal(true)} delay={300}>
              <View style={styles.statCard}>
                <View style={styles.statIcon}>
                  <TrendingUp size={20} color={Colors.light.success} />
                </View>
                {isLoading ? (
                  <ActivityIndicator
                    size="small"
                    color={Colors.light.primary}
                    style={commonStyles.marginVertical8}
                  />
                ) : (
                  <Text style={styles.statNumber}>
                    {metrics.averageSatisfaction > 0
                      ? `${Math.round(metrics.averageSatisfaction * 20)}%`
                      : '-'}
                  </Text>
                )}
                <Text style={styles.statLabel}>Satisfacción</Text>
              </View>
            </BreathingCard>

            <BreathingCard delay={400}>
              <View style={styles.statCard}>
                <View style={styles.statIcon}>
                  <Shield size={20} color={Colors.light.secondary} />
                </View>
                <View style={styles.privacyIndicator}>
                  <Text style={styles.statNumber}>🔒</Text>
                  <Text style={styles.privacyStatus}>Protegida</Text>
                </View>
                <Text style={styles.statLabel}>Privacidad</Text>
              </View>
            </BreathingCard>
          </View>
        </View>

        <View style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Características Destacadas</Text>

          <View style={styles.featureCard}>
            <View style={styles.featureIcon}>
              <Zap size={24} color={Colors.light.primary} />
            </View>
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>Análisis IA Profesional</Text>
              <Text style={styles.featureDescription}>
                Diagnóstico capilar avanzado con reconocimiento de imagen y recomendaciones
                personalizadas
              </Text>
            </View>
          </View>

          <View style={styles.featureCard}>
            <View style={styles.featureIcon}>
              <Shield size={24} color={Colors.light.success} />
            </View>
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>Privacidad Total</Text>
              <Text style={styles.featureDescription}>
                Política &quot;Analizar y Descartar&quot; - Las imágenes se procesan y eliminan inmediatamente
              </Text>
            </View>
          </View>

          <View style={styles.featureCard}>
            <View style={styles.featureIcon}>
              <Users size={24} color={Colors.light.secondary} />
            </View>
            <View style={styles.featureContent}>
              <Text style={styles.featureTitle}>Historial Inteligente</Text>
              <Text style={styles.featureDescription}>
                Seguimiento completo de clientes con alertas de alergias y recomendaciones
                personalizadas
              </Text>
            </View>
          </View>
        </View>

        {/* Bottom spacing for FAB */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Floating Action Button */}
      <FloatingActionButton />

      {/* Reports Modal */}
      <Modal visible={showReportsModal} animationType="slide" presentationStyle="pageSheet">
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowReportsModal(false)}>
              <X size={24} color={Colors.light.gray} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Reportes de Inventario</Text>
            <View style={commonStyles.width24} />
          </View>

          <Suspense fallback={<ReportsLoadingFallback />}>
            <LazyComponents.InventoryReports onProductSelect={handleProductSelect} />
          </Suspense>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
  },
  header: {
    padding: spacing.lg,
    paddingTop: 40,
    backgroundColor: Colors.light.background,
  },
  title: {
    fontSize: typography.sizes['4xl'],
    fontWeight: typography.weights.extrabold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  rainbowBg: {
    position: 'absolute',
    top: -4,
    left: -8,
    right: -8,
    bottom: -4,
    borderRadius: 12,
    backgroundColor: Colors.light.highlight,
  },
  easterEggProgress: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 4,
    gap: 4,
  },
  easterEggDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  subtitle: {
    fontSize: typography.sizes.base,
    color: Colors.light.textSecondary,
    fontWeight: typography.weights.medium,
  },
  quickActions: {
    padding: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.md,
  },
  primaryAction: {
    borderRadius: radius.lg,
    padding: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 4,
  },
  actionIcon: {
    width: 56,
    height: 56,
    borderRadius: radius.full,
    backgroundColor: Colors.light.primaryTransparent20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: Colors.light.textLight,
    marginBottom: spacing.xs,
  },
  actionSubtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textLight,
    lineHeight: 20,
  },
  secondaryActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  secondaryAction: {
    flex: 1,
    minWidth: 80,
    alignItems: 'center',
    backgroundColor: Colors.light.card,
    padding: spacing.md,
    borderRadius: radius.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  secondaryActionText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginTop: spacing.sm,
    textAlign: 'center',
  },
  statsSection: {
    padding: spacing.lg,
    paddingTop: 0,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    alignItems: 'center',
    padding: spacing.md,
    backgroundColor: Colors.light.card,
    borderRadius: radius.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: radius.full,
    backgroundColor: Colors.light.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  statNumber: {
    fontSize: typography.sizes['2xl'],
    fontWeight: typography.weights.extrabold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.sizes.xs,
    color: Colors.light.textSecondary,
    fontWeight: typography.weights.medium,
  },
  featuresSection: {
    padding: spacing.lg,
    paddingTop: 0,
  },
  featureCard: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
    padding: spacing.md,
    backgroundColor: Colors.light.card,
    borderRadius: radius.lg,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  featureIcon: {
    width: 48,
    height: 48,
    borderRadius: radius.full,
    backgroundColor: Colors.light.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing.md,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.bold,
    color: Colors.light.text,
    marginBottom: spacing.xs,
  },
  featureDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: Colors.light.card,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
  },
  privacyIndicator: {
    alignItems: 'center',
  },
  privacyStatus: {
    fontSize: typography.sizes.xs,
    color: Colors.light.success,
    fontWeight: typography.weights.medium,
    marginTop: 2,
  },
  bottomSpacing: {
    height: 100, // Space for FAB
  },

  // Floating Action Button styles
  fab: {
    position: 'absolute',
    right: 20,
    bottom: 30,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  fabBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.light.backdropColor,
    zIndex: 998,
  },
  quickActionsContainer: {
    position: 'absolute',
    right: 20,
    zIndex: 999,
  },
  quickActionItem: {
    position: 'absolute',
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
  },
  quickActionButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
  },
  quickActionLabel: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    backgroundColor: Colors.light.card,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: radius.md,
    marginRight: spacing.sm,
    shadowColor: Colors.light.text,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
});
