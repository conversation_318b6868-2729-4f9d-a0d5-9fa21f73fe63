/**
 * Chat Theme - Tema específico para la experiencia de chat
 * Optimizado para profesionalismo, confianza y legibilidad
 */

import { Platform } from 'react-native';
import Colors from './colors';

// Paleta de colores refinada para el chat
export const chatColors = {
  // Colores principales mejorados
  primary: {
    main: '#B8941F',
    light: '#E6C757',
    dark: '#8B6F1F',
    surface: '#FDF8E8', // Fondo suave dorado
    text: '#FFFFFF',
  },
  
  // Grises profesionales con mejor contraste
  neutral: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },
  
  // Colores semánticos
  semantic: {
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
  },
  
  // Colores específicos del chat
  chat: {
    userBubble: '#B8941F',
    userBubbleText: '#FFFFFF',
    assistantBubble: '#F5F5F5',
    assistantBubbleText: '#212121',
    inputBackground: '#FFFFFF',
    inputBorder: '#E0E0E0',
    inputBorderFocused: '#B8941F',
    quickActionBackground: '#FDF8E8',
    quickActionBorder: '#E6C757',
  },
};

// Tipografía optimizada para legibilidad
export const chatTypography = {
  // Familias de fuentes
  fontFamily: {
    regular: Platform.select({
      ios: 'SF Pro Text',
      android: 'Roboto',
      default: 'System',
    }),
    medium: Platform.select({
      ios: 'SF Pro Text',
      android: 'Roboto',
      default: 'System',
    }),
    bold: Platform.select({
      ios: 'SF Pro Text',
      android: 'Roboto',
      default: 'System',
    }),
    mono: Platform.select({
      ios: 'SF Mono',
      android: 'Roboto Mono',
      default: 'monospace',
    }),
  },
  
  // Tamaños optimizados para chat
  sizes: {
    xs: 11,
    sm: 13,
    base: 15,
    lg: 17,
    xl: 19,
    '2xl': 22,
    '3xl': 26,
    '4xl': 30,
  },
  
  // Pesos de fuente
  weights: {
    light: '300' as const,
    regular: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
    extrabold: '800' as const,
  },
  
  // Alturas de línea optimizadas
  lineHeights: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
};

// Espaciado consistente
export const chatSpacing = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  '3xl': 32,
  '4xl': 40,
  '5xl': 48,
  '6xl': 64,
};

// Radios de borde
export const chatRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 20,
  '3xl': 24,
  full: 9999,
  
  // Específicos para chat
  bubble: 18,
  input: 12,
  quickAction: 16,
};

// Sombras profesionales
export const chatShadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 6,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 10,
  },
  
  // Sombras específicas para elementos del chat
  bubble: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 3,
    elevation: 2,
  },
  fab: {
    shadowColor: '#B8941F',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
};

// Animaciones suaves
export const chatAnimations = {
  timing: {
    fast: 200,
    normal: 300,
    slow: 500,
  },
  
  easing: {
    easeInOut: 'ease-in-out',
    easeOut: 'ease-out',
    spring: 'spring',
  },
  
  // Configuraciones específicas
  bubble: {
    duration: 300,
    damping: 15,
    stiffness: 150,
  },
  
  quickAction: {
    duration: 200,
    damping: 12,
    stiffness: 200,
  },
  
  fab: {
    duration: 150,
    damping: 10,
    stiffness: 300,
  },
};

// Dimensiones específicas del chat
export const chatDimensions = {
  // Alturas mínimas para touch targets
  minTouchTarget: 44,
  
  // Tamaños específicos
  fabSize: 56,
  quickActionHeight: 60,
  inputMinHeight: 44,
  bubbleMaxWidth: '80%',
  
  // Espaciado interno
  bubblePadding: {
    horizontal: 16,
    vertical: 12,
  },
  
  inputPadding: {
    horizontal: 16,
    vertical: 12,
  },
};

// Tema completo del chat
export const chatTheme = {
  colors: chatColors,
  typography: chatTypography,
  spacing: chatSpacing,
  radius: chatRadius,
  shadows: chatShadows,
  animations: chatAnimations,
  dimensions: chatDimensions,
};

// Utilidades para crear estilos consistentes
export const createChatBubbleStyle = (isUser: boolean) => ({
  backgroundColor: isUser ? chatColors.chat.userBubble : chatColors.chat.assistantBubble,
  borderRadius: chatRadius.bubble,
  paddingHorizontal: chatDimensions.bubblePadding.horizontal,
  paddingVertical: chatDimensions.bubblePadding.vertical,
  maxWidth: chatDimensions.bubbleMaxWidth,
  ...chatShadows.bubble,
});

export const createQuickActionStyle = () => ({
  backgroundColor: chatColors.chat.quickActionBackground,
  borderRadius: chatRadius.quickAction,
  borderWidth: 1,
  borderColor: chatColors.chat.quickActionBorder,
  height: chatDimensions.quickActionHeight,
  paddingHorizontal: chatSpacing.md,
  ...chatShadows.sm,
});

export const createInputStyle = (focused: boolean = false) => ({
  backgroundColor: chatColors.chat.inputBackground,
  borderRadius: chatRadius.input,
  borderWidth: 1,
  borderColor: focused ? chatColors.chat.inputBorderFocused : chatColors.chat.inputBorder,
  minHeight: chatDimensions.inputMinHeight,
  paddingHorizontal: chatDimensions.inputPadding.horizontal,
  paddingVertical: chatDimensions.inputPadding.vertical,
  fontSize: chatTypography.sizes.base,
  fontFamily: chatTypography.fontFamily.regular,
  color: chatColors.neutral[900],
});

export default chatTheme;
