/**
 * Whimsy Effects - Micro-interacciones deliciosas para mejorar la UX
 * Incluye animaciones fluidas, haptic feedback, y efectos visuales profesionales
 */

import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  withSequence,
  withDelay,
  withRepeat,
  interpolate,
  runOnJS,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

import Colors from '@/constants/colors';
import { useHapticsEnabled, useRippleEffectsEnabled } from '@/stores/whimsy-store';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface WhimsyEffectsProps {
  children: React.ReactNode;
  enableParticles?: boolean;
  enableRipples?: boolean;
  enableBreathing?: boolean;
}

// Componente de partículas flotantes para momentos especiales
const FloatingParticles: React.FC<{ visible: boolean }> = ({ visible }) => {
  const particles = Array.from({ length: 8 }, (_, i) => ({
    id: i,
    x: useSharedValue(Math.random() * SCREEN_WIDTH),
    y: useSharedValue(SCREEN_HEIGHT + 50),
    opacity: useSharedValue(0),
    scale: useSharedValue(0.5 + Math.random() * 0.5),
  }));

  useEffect(() => {
    if (visible) {
      particles.forEach((particle, index) => {
        particle.opacity.value = withDelay(
          index * 200,
          withTiming(0.6, { duration: 1000 })
        );
        
        particle.y.value = withDelay(
          index * 200,
          withTiming(-50, { duration: 3000 + Math.random() * 2000 })
        );
        
        particle.x.value = withRepeat(
          withTiming(particle.x.value + (Math.random() - 0.5) * 100, {
            duration: 2000 + Math.random() * 1000,
          }),
          -1,
          true
        );
      });
    } else {
      particles.forEach(particle => {
        particle.opacity.value = withTiming(0, { duration: 500 });
      });
    }
  }, [visible]);

  return (
    <View style={styles.particlesContainer} pointerEvents="none">
      {particles.map((particle) => {
        const animatedStyle = useAnimatedStyle(() => ({
          transform: [
            { translateX: particle.x.value },
            { translateY: particle.y.value },
            { scale: particle.scale.value },
          ],
          opacity: particle.opacity.value,
        }));

        return (
          <Animated.View
            key={particle.id}
            style={[styles.particle, animatedStyle]}
          />
        );
      })}
    </View>
  );
};

// Efecto de ondas (ripple) para interacciones
const RippleEffect: React.FC<{ 
  x: number; 
  y: number; 
  visible: boolean; 
  onComplete: () => void;
}> = ({ x, y, visible, onComplete }) => {
  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);

  useEffect(() => {
    if (visible) {
      scale.value = 0;
      opacity.value = 0.6;
      
      scale.value = withTiming(1, { duration: 600 }, (finished) => {
        if (finished) {
          runOnJS(onComplete)();
        }
      });
      
      opacity.value = withTiming(0, { duration: 600 });
    }
  }, [visible]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: x - 50 },
      { translateY: y - 50 },
      { scale: scale.value },
    ],
    opacity: opacity.value,
  }));

  if (!visible) return null;

  return (
    <Animated.View style={[styles.ripple, animatedStyle]} pointerEvents="none" />
  );
};

// Efecto de respiración para elementos importantes
const BreathingEffect: React.FC<{ children: React.ReactNode; enabled: boolean }> = ({ 
  children, 
  enabled 
}) => {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  useEffect(() => {
    if (enabled) {
      scale.value = withRepeat(
        withSequence(
          withTiming(1.02, { duration: 2000 }),
          withTiming(1, { duration: 2000 })
        ),
        -1,
        false
      );
      
      opacity.value = withRepeat(
        withSequence(
          withTiming(0.9, { duration: 2000 }),
          withTiming(1, { duration: 2000 })
        ),
        -1,
        false
      );
    } else {
      scale.value = withTiming(1, { duration: 500 });
      opacity.value = withTiming(1, { duration: 500 });
    }
  }, [enabled]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={animatedStyle}>
      {children}
    </Animated.View>
  );
};

// Componente principal que orquesta todos los efectos
export const WhimsyEffects: React.FC<WhimsyEffectsProps> = ({
  children,
  enableParticles = false,
  enableRipples = true,
  enableBreathing = false,
}) => {
  const hapticsEnabled = useHapticsEnabled();
  const rippleEnabled = useRippleEffectsEnabled();
  
  const [ripples, setRipples] = React.useState<Array<{
    id: number;
    x: number;
    y: number;
    visible: boolean;
  }>>([]);

  const addRipple = (x: number, y: number) => {
    if (!rippleEnabled || !enableRipples) return;
    
    const id = Date.now();
    setRipples(prev => [...prev, { id, x, y, visible: true }]);
    
    if (hapticsEnabled) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const removeRipple = (id: number) => {
    setRipples(prev => prev.filter(ripple => ripple.id !== id));
  };

  return (
    <View style={styles.container}>
      <BreathingEffect enabled={enableBreathing}>
        {children}
      </BreathingEffect>
      
      {/* Efectos de ondas */}
      {ripples.map(ripple => (
        <RippleEffect
          key={ripple.id}
          x={ripple.x}
          y={ripple.y}
          visible={ripple.visible}
          onComplete={() => removeRipple(ripple.id)}
        />
      ))}
      
      {/* Partículas flotantes */}
      <FloatingParticles visible={enableParticles} />
    </View>
  );
};

// Hook para usar efectos whimsy fácilmente
export const useWhimsyEffects = () => {
  const hapticsEnabled = useHapticsEnabled();
  
  const triggerHaptic = (type: 'light' | 'medium' | 'heavy' = 'light') => {
    if (!hapticsEnabled) return;
    
    switch (type) {
      case 'light':
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        break;
      case 'medium':
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        break;
      case 'heavy':
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
        break;
    }
  };

  const triggerSuccess = () => {
    if (hapticsEnabled) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
  };

  const triggerError = () => {
    if (hapticsEnabled) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  };

  return {
    triggerHaptic,
    triggerSuccess,
    triggerError,
  };
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  
  particlesContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  
  particle: {
    position: 'absolute',
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: Colors.light.primary,
  },
  
  ripple: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.light.primary,
    zIndex: 999,
  },
});

export default WhimsyEffects;
