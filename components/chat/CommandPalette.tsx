/**
 * Command Palette - Sistema de comandos rápidos tipo /command
 * Permite acceso rápido a funcionalidades desde el chat
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  TextInput,
} from 'react-native';
import {
  Users,
  Package,
  Zap,
  BarChart3,
  Palette,
  Search,
  Clock,
  Settings,
  Camera,
  FileText,
} from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

import Colors from '@/constants/colors';
import { spacing, radius, shadows, typography } from '@/constants/theme';

interface Command {
  id: string;
  command: string;
  description: string;
  icon: React.ComponentType<any>;
  category: 'client' | 'service' | 'inventory' | 'report' | 'system';
  action: (params?: string) => void;
  params?: string[];
}

interface CommandPaletteProps {
  visible: boolean;
  onClose: () => void;
  onExecuteCommand: (command: Command, params?: string) => void;
  inputValue: string;
}

const COMMANDS: Command[] = [
  {
    id: 'new-service',
    command: '/nuevo-servicio',
    description: 'Iniciar un nuevo servicio de coloración',
    icon: Zap,
    category: 'service',
    action: () => {},
  },
  {
    id: 'search-client',
    command: '/cliente',
    description: 'Buscar o crear cliente',
    icon: Users,
    category: 'client',
    action: () => {},
    params: ['nombre'],
  },
  {
    id: 'search-inventory',
    command: '/inventario',
    description: 'Buscar productos en inventario',
    icon: Package,
    category: 'inventory',
    action: () => {},
    params: ['producto'],
  },
  {
    id: 'generate-formula',
    command: '/formula',
    description: 'Generar fórmula de coloración',
    icon: Palette,
    category: 'service',
    action: () => {},
    params: ['descripción'],
  },
  {
    id: 'take-photo',
    command: '/foto',
    description: 'Tomar foto para diagnóstico',
    icon: Camera,
    category: 'service',
    action: () => {},
  },
  {
    id: 'monthly-report',
    command: '/reporte',
    description: 'Generar reporte de ventas',
    icon: BarChart3,
    category: 'report',
    action: () => {},
    params: ['tipo'],
  },
  {
    id: 'recent-services',
    command: '/recientes',
    description: 'Ver servicios recientes',
    icon: Clock,
    category: 'service',
    action: () => {},
  },
  {
    id: 'settings',
    command: '/configuracion',
    description: 'Abrir configuración',
    icon: Settings,
    category: 'system',
    action: () => {},
  },
];

const AnimatedView = Animated.createAnimatedComponent(View);

export const CommandPalette: React.FC<CommandPaletteProps> = ({
  visible,
  onClose,
  onExecuteCommand,
  inputValue,
}) => {
  const [filteredCommands, setFilteredCommands] = useState<Command[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  
  const translateY = useSharedValue(visible ? 0 : -300);
  const opacity = useSharedValue(visible ? 1 : 0);

  useEffect(() => {
    translateY.value = withSpring(visible ? 0 : -300, {
      damping: 15,
      stiffness: 150,
    });
    opacity.value = withTiming(visible ? 1 : 0, { duration: 200 });
  }, [visible]);

  useEffect(() => {
    if (!inputValue.startsWith('/')) {
      setFilteredCommands([]);
      return;
    }

    const query = inputValue.slice(1).toLowerCase();
    const filtered = COMMANDS.filter(cmd =>
      cmd.command.slice(1).toLowerCase().includes(query) ||
      cmd.description.toLowerCase().includes(query)
    );
    
    setFilteredCommands(filtered);
    setSelectedIndex(0);
  }, [inputValue]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
    opacity: opacity.value,
  }));

  const handleCommandPress = (command: Command) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    onExecuteCommand(command);
    onClose();
  };

  const getCategoryColor = (category: Command['category']) => {
    const colors = {
      client: Colors.light.secondary,
      service: Colors.light.primary,
      inventory: '#3B82F6',
      report: '#10B981',
      system: '#6B7280',
    };
    return colors[category];
  };

  const renderCommand = ({ item, index }: { item: Command; index: number }) => {
    const IconComponent = item.icon;
    const isSelected = index === selectedIndex;
    const categoryColor = getCategoryColor(item.category);

    return (
      <TouchableOpacity
        style={[
          styles.commandItem,
          isSelected && styles.commandItemSelected,
        ]}
        onPress={() => handleCommandPress(item)}
        activeOpacity={0.7}
      >
        <View style={styles.commandIcon}>
          <IconComponent
            size={18}
            color={categoryColor}
          />
        </View>
        
        <View style={styles.commandContent}>
          <Text style={styles.commandText}>
            {item.command}
            {item.params && (
              <Text style={styles.commandParams}>
                {' '}{item.params.map(p => `[${p}]`).join(' ')}
              </Text>
            )}
          </Text>
          <Text style={styles.commandDescription}>
            {item.description}
          </Text>
        </View>
        
        <View style={[styles.categoryBadge, { backgroundColor: categoryColor + '20' }]}>
          <Text style={[styles.categoryText, { color: categoryColor }]}>
            {item.category}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  if (!visible || filteredCommands.length === 0) return null;

  return (
    <AnimatedView style={[styles.container, animatedStyle]}>
      <View style={styles.header}>
        <Text style={styles.title}>Comandos Disponibles</Text>
        <Text style={styles.subtitle}>
          Selecciona un comando o continúa escribiendo
        </Text>
      </View>
      
      <FlatList
        data={filteredCommands}
        renderItem={renderCommand}
        keyExtractor={item => item.id}
        style={styles.commandsList}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      />
    </AnimatedView>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 60,
    left: spacing[4],
    right: spacing[4],
    backgroundColor: Colors.light.surface,
    borderRadius: radius.xl,
    ...shadows.lg,
    maxHeight: 300,
    zIndex: 1000,
  },
  
  header: {
    padding: spacing[4],
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  
  title: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: Colors.light.text,
    marginBottom: spacing[1],
  },
  
  subtitle: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
  },
  
  commandsList: {
    maxHeight: 200,
  },
  
  commandItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border + '30',
  },
  
  commandItemSelected: {
    backgroundColor: Colors.light.primary + '10',
  },
  
  commandIcon: {
    width: 32,
    height: 32,
    borderRadius: radius.md,
    backgroundColor: Colors.light.background,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing[3],
  },
  
  commandContent: {
    flex: 1,
  },
  
  commandText: {
    fontSize: typography.sizes.base,
    fontWeight: typography.weights.medium,
    color: Colors.light.text,
    fontFamily: 'monospace',
  },
  
  commandParams: {
    color: Colors.light.textSecondary,
    fontStyle: 'italic',
  },
  
  commandDescription: {
    fontSize: typography.sizes.sm,
    color: Colors.light.textSecondary,
    marginTop: spacing[1],
  },
  
  categoryBadge: {
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: radius.sm,
  },
  
  categoryText: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
    textTransform: 'uppercase',
  },
});

export default CommandPalette;
