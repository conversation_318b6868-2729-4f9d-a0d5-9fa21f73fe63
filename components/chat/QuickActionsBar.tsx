/**
 * Quick Actions Bar - Acciones contextuales en el chat
 * Aparecen dinámicamente según el contexto de la conversación
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  Users,
  Package,
  Zap,
  Bar<PERSON>hart3,
  Camera,
  Palette,
  Clock,
  Plus,
} from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withDelay,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

import Colors from '@/constants/colors';
import { spacing, radius, shadows } from '@/constants/theme';
import { chatTheme, createQuickActionStyle } from '@/constants/ChatTheme';

interface QuickAction {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  color: string;
  onPress: () => void;
  badge?: string;
}

interface QuickActionsBarProps {
  actions: QuickAction[];
  visible: boolean;
  contextualMessage?: string;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

export const QuickActionsBar: React.FC<QuickActionsBarProps> = ({
  actions,
  visible,
  contextualMessage,
}) => {
  const translateY = useSharedValue(visible ? 0 : 100);
  const opacity = useSharedValue(visible ? 1 : 0);

  React.useEffect(() => {
    translateY.value = withSpring(visible ? 0 : 100, {
      damping: 15,
      stiffness: 150,
    });
    opacity.value = withSpring(visible ? 1 : 0);
  }, [visible]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
    opacity: opacity.value,
  }));

  const handleActionPress = (action: QuickAction) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    action.onPress();
  };

  if (!visible || actions.length === 0) return null;

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      {contextualMessage && (
        <View style={styles.messageContainer}>
          <Text style={styles.messageText}>{contextualMessage}</Text>
        </View>
      )}
      
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.actionsContainer}
      >
        {actions.map((action, index) => {
          const IconComponent = action.icon;
          
          return (
            <AnimatedTouchableOpacity
              key={action.id}
              style={[
                styles.actionButton,
                { backgroundColor: action.color + '15' }
              ]}
              onPress={() => handleActionPress(action)}
              activeOpacity={0.7}
            >
              <View style={styles.iconContainer}>
                <IconComponent
                  size={20}
                  color={action.color}
                />
                {action.badge && (
                  <View style={styles.badge}>
                    <Text style={styles.badgeText}>{action.badge}</Text>
                  </View>
                )}
              </View>
              <Text style={[styles.actionTitle, { color: action.color }]}>
                {action.title}
              </Text>
            </AnimatedTouchableOpacity>
          );
        })}
      </ScrollView>
    </Animated.View>
  );
};

// Sistema inteligente de acciones contextuales
export const getContextualActions = (context: string, lastMessage?: string): QuickAction[] => {
  const baseActions: QuickAction[] = [
    {
      id: 'new-service',
      title: 'Nuevo Servicio',
      icon: Zap,
      color: Colors.light.primary,
      onPress: () => {},
    },
    {
      id: 'clients',
      title: 'Clientes',
      icon: Users,
      color: Colors.light.secondary,
      onPress: () => {},
    },
    {
      id: 'inventory',
      title: 'Inventario',
      icon: Package,
      color: '#3B82F6',
      onPress: () => {},
    },
    {
      id: 'reports',
      title: 'Reportes',
      icon: BarChart3,
      color: '#10B981',
      onPress: () => {},
    },
  ];

  // Detección inteligente de contexto basada en palabras clave
  const detectContext = (message: string): string[] => {
    const msg = message.toLowerCase();
    const contexts: string[] = [];

    // Detección de nombres (posibles clientes nuevos)
    if (msg.includes('cliente') && (msg.includes('nueva') || msg.includes('nuevo'))) {
      contexts.push('new-client');
    }

    // Detección de diagnóstico
    if (msg.includes('diagnóstico') || msg.includes('foto') || msg.includes('analizar') || msg.includes('cabello')) {
      contexts.push('diagnosis');
    }

    // Detección de formulación
    if (msg.includes('fórmula') || msg.includes('color') || msg.includes('tinte') || msg.includes('mechas')) {
      contexts.push('formulation');
    }

    // Detección de inventario
    if (msg.includes('producto') || msg.includes('stock') || msg.includes('inventario')) {
      contexts.push('inventory');
    }

    return contexts;
  };

  // Acciones contextuales específicas
  const contextualActions: Record<string, QuickAction[]> = {
    'new-client': [
      {
        id: 'create-client',
        title: 'Crear Cliente',
        icon: Plus,
        color: Colors.light.primary,
        onPress: () => {},
      },
    ],
    'diagnosis': [
      {
        id: 'take-photo',
        title: 'Tomar Foto',
        icon: Camera,
        color: '#F59E0B',
        onPress: () => {},
      },
    ],
    'formulation': [
      {
        id: 'color-wheel',
        title: 'Rueda Color',
        icon: Palette,
        color: '#EF4444',
        onPress: () => {},
      },
    ],
    'inventory': [
      {
        id: 'search-product',
        title: 'Buscar Producto',
        icon: Package,
        color: '#3B82F6',
        onPress: () => {},
      },
    ],
  };

  // Si hay un mensaje, detectar contextos automáticamente
  if (lastMessage) {
    const detectedContexts = detectContext(lastMessage);
    const contextActions: QuickAction[] = [];

    detectedContexts.forEach(ctx => {
      if (contextualActions[ctx]) {
        contextActions.push(...contextualActions[ctx]);
      }
    });

    // Combinar acciones contextuales con acciones base (sin duplicados)
    const allActions = [...contextActions, ...baseActions];
    const uniqueActions = allActions.filter((action, index, self) =>
      index === self.findIndex(a => a.id === action.id)
    );

    return uniqueActions.slice(0, 4); // Máximo 4 acciones
  }

  // Fallback a acciones por contexto específico o base
  return contextualActions[context] ?
    [...contextualActions[context], ...baseActions].slice(0, 4) :
    baseActions;
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 80, // Encima del input
    left: 0,
    right: 0,
    backgroundColor: Colors.light.surface,
    borderTopLeftRadius: radius.xl,
    borderTopRightRadius: radius.xl,
    ...shadows.lg,
    paddingVertical: spacing[3],
  },
  
  messageContainer: {
    paddingHorizontal: spacing[4],
    paddingBottom: spacing[2],
  },
  
  messageText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  
  actionsContainer: {
    paddingHorizontal: spacing[4],
    gap: spacing[3],
  },
  
  actionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing[3],
    paddingHorizontal: spacing[4],
    borderRadius: radius.lg,
    minWidth: 80,
    position: 'relative',
  },
  
  iconContainer: {
    position: 'relative',
    marginBottom: spacing[1],
  },
  
  badge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: Colors.light.error,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  badgeText: {
    color: Colors.light.surface,
    fontSize: 10,
    fontWeight: '600',
  },
  
  actionTitle: {
    fontSize: 11,
    fontWeight: '500',
    textAlign: 'center',
  },
});

export default QuickActionsBar;
