/**
 * Chat Hub - Nueva pantalla principal centrada en el chat
 * Reemplaza el dashboard tradicional con una experiencia conversacional
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import {
  Sparkles,
  Plus,
  Users,
  Package,
  BarChart3,
  Zap,
  MessageCircle,
} from 'lucide-react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withDelay,
  interpolate,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as Haptics from 'expo-haptics';

import Colors from '@/constants/colors';
import { spacing, radius, shadows, typography } from '@/constants/theme';
import { chatTheme } from '@/constants/ChatTheme';
import { ChatGPTInterface } from './ChatGPTInterface';
import { QuickActionsBar, getContextualActions } from './QuickActionsBar';
import { CommandPalette } from './CommandPalette';
import { useChatStore } from '@/stores/chat-store';
import { WhimsyEffects, useWhimsyEffects } from '@/components/ui/WhimsyEffects';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface ChatHubProps {
  onNavigateToService: () => void;
  onNavigateToClients: () => void;
  onNavigateToInventory: () => void;
  onNavigateToReports: () => void;
}

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

export const ChatHub: React.FC<ChatHubProps> = ({
  onNavigateToService,
  onNavigateToClients,
  onNavigateToInventory,
  onNavigateToReports,
}) => {
  const insets = useSafeAreaInsets();
  const { conversations, currentConversationId } = useChatStore();
  const { triggerHaptic, triggerSuccess } = useWhimsyEffects();
  
  const [showQuickActions, setShowQuickActions] = useState(true);
  const [showCommandPalette, setShowCommandPalette] = useState(false);
  const [chatContext, setChatContext] = useState('default');
  const [inputValue, setInputValue] = useState('');
  const [lastMessage, setLastMessage] = useState('');

  // Animaciones para el FAB y elementos flotantes
  const fabScale = useSharedValue(1);
  const fabRotation = useSharedValue(0);
  const headerOpacity = useSharedValue(1);

  const currentConversation = conversations.find(c => c.id === currentConversationId);
  const hasActiveChat = currentConversation && currentConversation.messages.length > 0;

  useEffect(() => {
    // Detectar contexto de la conversación para mostrar acciones relevantes
    if (currentConversation?.messages.length) {
      const lastMsg = currentConversation.messages[currentConversation.messages.length - 1];
      setLastMessage(lastMsg.content);

      // El contexto ahora se detecta automáticamente en getContextualActions
      if (lastMsg.content.toLowerCase().includes('cliente')) {
        setChatContext('new-client');
      } else if (lastMsg.content.toLowerCase().includes('diagnóstico') ||
                 lastMsg.content.toLowerCase().includes('foto')) {
        setChatContext('diagnosis');
      } else if (lastMsg.content.toLowerCase().includes('fórmula') ||
                 lastMsg.content.toLowerCase().includes('color')) {
        setChatContext('formulation');
      } else {
        setChatContext('default');
      }
    }
  }, [currentConversation]);

  const handleFabPress = () => {
    triggerHaptic('medium');
    fabScale.value = withSpring(0.9, { duration: 100 }, () => {
      fabScale.value = withSpring(1);
    });
    triggerSuccess();
    onNavigateToService();
  };

  const handleInputChange = (text: string) => {
    setInputValue(text);
    setShowCommandPalette(text.startsWith('/'));
  };

  const handleExecuteCommand = (command: any, params?: string) => {
    // Implementar ejecución de comandos
    switch (command.id) {
      case 'new-service':
        onNavigateToService();
        break;
      case 'search-client':
        onNavigateToClients();
        break;
      case 'search-inventory':
        onNavigateToInventory();
        break;
      case 'monthly-report':
        onNavigateToReports();
        break;
      default:
        console.log('Comando no implementado:', command.id);
    }
  };

  const fabAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: fabScale.value },
      { rotate: `${fabRotation.value}deg` },
    ],
  }));

  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
  }));

  const quickActions = getContextualActions(chatContext, lastMessage);

  return (
    <WhimsyEffects
      enableBreathing={hasActiveChat}
      enableRipples={true}
    >
      <View style={[styles.container, { paddingTop: insets.top }]}>
        {/* Header minimalista */}
        <Animated.View style={[styles.header, headerAnimatedStyle]}>
          <View style={styles.headerContent}>
            <View style={styles.logoContainer}>
              <Sparkles size={24} color={Colors.light.primary} />
              <Text style={styles.logoText}>Salonier</Text>
            </View>

            {hasActiveChat && (
              <TouchableOpacity
                style={styles.conversationIndicator}
                onPress={() => {
                  triggerHaptic('light');
                  /* Mostrar lista de conversaciones */
                }}
              >
                <MessageCircle size={20} color={Colors.light.textSecondary} />
                <Text style={styles.conversationCount}>
                  {conversations.length}
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </Animated.View>

      {/* Chat Interface Principal */}
      <View style={styles.chatContainer}>
        <ChatGPTInterface
          onInputChange={handleInputChange}
          onContextChange={setChatContext}
          showCommandPalette={showCommandPalette}
          onCommandPaletteChange={setShowCommandPalette}
        />
      </View>

      {/* Command Palette */}
      <CommandPalette
        visible={showCommandPalette}
        onClose={() => setShowCommandPalette(false)}
        onExecuteCommand={handleExecuteCommand}
        inputValue={inputValue}
      />

      {/* Quick Actions Bar */}
      <QuickActionsBar
        actions={quickActions.map(action => ({
          ...action,
          onPress: () => {
            switch (action.id) {
              case 'new-service':
                onNavigateToService();
                break;
              case 'clients':
                onNavigateToClients();
                break;
              case 'inventory':
                onNavigateToInventory();
                break;
              case 'reports':
                onNavigateToReports();
                break;
              default:
                action.onPress();
            }
          },
        }))}
        visible={showQuickActions && !showCommandPalette}
        contextualMessage={
          chatContext !== 'default' 
            ? `Acciones sugeridas para ${chatContext}`
            : undefined
        }
      />

      {/* Floating Action Button - Nuevo Servicio */}
      <AnimatedTouchableOpacity
        style={[styles.fab, fabAnimatedStyle]}
        onPress={handleFabPress}
        activeOpacity={0.8}
      >
        <Plus size={24} color={Colors.light.surface} />
      </AnimatedTouchableOpacity>

      {/* Indicador de estado del chat */}
      {hasActiveChat && (
        <View style={styles.chatStatusIndicator}>
          <View style={styles.statusDot} />
          <Text style={styles.statusText}>Chat activo</Text>
        </View>
      )}
      </View>
    </WhimsyEffects>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: chatTheme.colors.neutral[50],
  },

  header: {
    paddingHorizontal: chatTheme.spacing.lg,
    paddingVertical: chatTheme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: chatTheme.colors.neutral[200],
    backgroundColor: chatTheme.colors.neutral[50],
  },

  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },

  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: chatTheme.spacing.sm,
  },

  logoText: {
    fontSize: chatTheme.typography.sizes['2xl'],
    fontWeight: chatTheme.typography.weights.bold,
    fontFamily: chatTheme.typography.fontFamily.bold,
    color: chatTheme.colors.neutral[900],
    letterSpacing: -0.5,
  },
  
  conversationIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: chatTheme.spacing.xs,
    paddingHorizontal: chatTheme.spacing.sm,
    paddingVertical: chatTheme.spacing.xs,
    backgroundColor: chatTheme.colors.primary.surface,
    borderRadius: chatTheme.radius.lg,
    borderWidth: 1,
    borderColor: chatTheme.colors.primary.light,
  },

  conversationCount: {
    fontSize: chatTheme.typography.sizes.sm,
    fontWeight: chatTheme.typography.weights.semibold,
    fontFamily: chatTheme.typography.fontFamily.medium,
    color: chatTheme.colors.primary.main,
  },

  chatContainer: {
    flex: 1,
  },

  fab: {
    position: 'absolute',
    bottom: 160, // Encima de las quick actions
    right: chatTheme.spacing.lg,
    width: chatTheme.dimensions.fabSize,
    height: chatTheme.dimensions.fabSize,
    borderRadius: chatTheme.dimensions.fabSize / 2,
    backgroundColor: chatTheme.colors.primary.main,
    alignItems: 'center',
    justifyContent: 'center',
    ...chatTheme.shadows.fab,
  },
  
  chatStatusIndicator: {
    position: 'absolute',
    top: 100,
    right: chatTheme.spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    gap: chatTheme.spacing.xs,
    paddingHorizontal: chatTheme.spacing.sm,
    paddingVertical: chatTheme.spacing.xs,
    backgroundColor: chatTheme.colors.semantic.success + '15',
    borderRadius: chatTheme.radius.lg,
    borderWidth: 1,
    borderColor: chatTheme.colors.semantic.success + '30',
    ...chatTheme.shadows.sm,
  },

  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: chatTheme.colors.semantic.success,
  },

  statusText: {
    fontSize: chatTheme.typography.sizes.xs,
    color: chatTheme.colors.semantic.success,
    fontWeight: chatTheme.typography.weights.semibold,
    fontFamily: chatTheme.typography.fontFamily.medium,
  },
});

export default ChatHub;
